{"Metadata": {"name": "绘盈", "title": "Huiying - AI虚拟试衣平台", "description": "体验未来时尚购物，Huiying的AI虚拟试衣技术让您在线试穿，真实感受服装效果"}, "Common": {"login": "登录", "logout": "退出", "signUp": "注册", "language": "切换语言", "mode": {"label": "切换模式", "light": "浅色模式", "dark": "深色模式", "system": "跟随系统"}, "theme": {"label": "切换主题", "default": "默认", "blue": "蓝色", "green": "绿色", "amber": "橙色", "neutral": "中性"}, "copy": "复制", "save": "保存", "saving": "保存中...", "loading": "加载中...", "cancel": "取消", "logoutFailed": "退出失败", "table": {"totalRecords": "总共 {count} 条记录", "noResults": "无结果", "loading": "加载中...", "columns": "列", "rowsPerPage": "每页行数", "page": "页", "firstPage": "第一页", "lastPage": "最后一页", "nextPage": "下一页", "previousPage": "上一页", "ascending": "升序", "descending": "降序"}}, "PricingPage": {"title": "产品定价", "description": "选择最适合您业务的方案，将Huiying的AI虚拟试衣技术集成到您的时尚电商平台", "subtitle": "灵活的定价方案，适合各种规模的企业", "monthly": "月付", "yearly": "年付", "PricingCard": {"freePrice": "$0", "perMonth": "/月", "perYear": "/年", "popular": "热门", "currentPlan": "当前方案", "yourCurrentPlan": "您的当前方案", "getStartedForFree": "开始免费使用", "getLifetimeAccess": "开启终身会员", "getStarted": "开始免费试用", "notAvailable": "不可用", "daysTrial": "{days} 天试用期"}, "CheckoutButton": {"loading": "加载中...", "checkoutFailed": "打开结账页面失败"}}, "PricePlans": {"free": {"name": "免费版", "description": "适用于个人使用的基本功能", "features": {"feature-1": "最多3个项目", "feature-2": "1GB存储空间", "feature-3": "基础分析功能", "feature-4": "社区支持"}, "limits": {"limit-1": "不支持自定义域名", "limit-2": "不支持自定义品牌", "limit-3": "不支持终身更新"}}, "pro": {"name": "专业版", "description": "专业人士的高级功能", "features": {"feature-1": "无限项目", "feature-2": "10GB存储空间", "feature-3": "高级分析功能", "feature-4": "优先支持", "feature-5": "自定义域名"}, "limits": {"limit-1": "不支持自定义品牌", "limit-2": "不支持终身更新"}}, "lifetime": {"name": "终身版", "description": "一次性付款获得所有高级功能", "features": {"feature-1": "所有专业版功能", "feature-2": "100GB存储空间", "feature-3": "专属支持", "feature-4": "企业级安全", "feature-5": "高级集成", "feature-6": "自定义品牌", "feature-7": "终身更新"}}}, "CreditPackages": {"basic": {"name": "基础版", "description": "基础版功能介绍放这里"}, "standard": {"name": "标准版", "description": "标准版功能介绍放这里"}, "premium": {"name": "高级版", "description": "高级版功能介绍放这里"}, "enterprise": {"name": "企业版", "description": "企业版功能介绍放这里"}}, "NotFoundPage": {"title": "404", "message": "抱歉，您正在寻找的页面不存在", "backToHome": "返回首页"}, "ErrorPage": {"title": "哎呀！出错了！", "tryAgain": "重试", "backToHome": "返回首页"}, "AboutPage": {"title": "关于我们", "description": "Huiying通过前沿的AI虚拟试衣技术革新时尚零售，让在线购物更加生动有趣，增强消费者购买信心。", "authorName": "<PERSON><PERSON>", "authorBio": "AI虚拟试衣平台", "introduction": "👋 你好，这里是Huiying，领先的AI虚拟试衣平台，我们运用先进的计算机视觉和机器学习技术，创造逼真的虚拟试衣体验，改变人们在线购买服装的方式。如果您有任何问题，欢迎联系我们。", "talkWithMe": "联系我们", "followMe": "关注我们"}, "OutfitPage": {"title": "穿搭画廊", "description": "通过AI虚拟试衣技术发现完美的服装搭配。浏览精选风格，看看它们在您身上的效果。", "uploadPhoto": "上传照片", "tryDemo": "体验演示", "features": {"upload": {"title": "上传照片", "description": "上传您的照片，我们的AI将为您分析最佳的服装搭配方案"}, "ai": {"title": "AI智能匹配", "description": "先进的AI算法分析您的体型和风格，推荐最适合的服装搭配"}, "result": {"title": "预览效果", "description": "实时预览服装穿着效果，让您在购买前就能看到完美的搭配"}}, "gallery": {"title": "风格画廊", "subtitle": "发现您的完美造型", "description": "浏览我们精心策划的服装搭配集合，通过AI虚拟试衣看看它们的效果", "filters": {"all": "全部", "male": "男装", "female": "女装"}, "buttons": {"buyNow": "立即购买", "tryOn": "试穿"}, "modal": {"title": "服装详情", "close": "关闭"}}, "comingSoon": {"title": "即将上线", "description": "我们正在努力开发这个功能，敬请期待。您可以订阅我们的通知，第一时间了解上线消息。", "notify": "订阅通知"}}, "ChangelogPage": {"title": "更新日志", "description": "查看我们产品的最新动态", "subtitle": "查看我们产品的最新动态"}, "ContactPage": {"title": "联系我们", "description": "我们帮助您找到合适的计划", "subtitle": "我们帮助您找到合适的计划", "form": {"title": "联系我们", "description": "如果您有任何问题或反馈，请随时联系我们的团队", "name": "姓名", "email": "邮箱", "message": "消息", "submit": "提交", "submitting": "提交中...", "success": "消息发送成功", "fail": "消息发送失败", "nameMinLength": "姓名必须至少包含 3 个字符", "nameMaxLength": "姓名必须不超过 30 个字符", "emailValidation": "请输入有效的邮箱地址", "messageMinLength": "消息必须至少包含 10 个字符", "messageMaxLength": "消息必须不超过 500 个字符"}}, "WaitlistPage": {"title": "等待队列", "description": "加入等待队列，及时获取最新消息和更新", "subtitle": "加入等待队列，及时获取最新消息和更新", "form": {"title": "加入等待队列", "description": "我们将在产品发布时及时通知您", "email": "邮箱", "subscribe": "加入等待队列", "subscribing": "加入等待队列中...", "success": "加入等待队列成功", "fail": "加入等待队列失败", "emailValidation": "请输入有效的邮箱地址"}}, "Newsletter": {"title": "邮件列表", "subtitle": "加入我们的社区", "description": "订阅邮件列表，及时获取最新消息和更新", "form": {"email": "邮箱", "subscribe": "订阅", "subscribing": "订阅中...", "success": "订阅成功", "fail": "订阅失败", "emailValidation": "请输入有效的邮箱地址"}}, "AuthPage": {"login": {"title": "登录", "welcomeBack": "欢迎回来", "email": "邮箱", "password": "密码", "signIn": "登录", "signUpHint": "没有账号？注册", "forgotPassword": "忘记密码？", "signInWithGoogle": "使用 Google 登录", "signInWithGitHub": "使用 GitHub 登录", "showPassword": "显示密码", "hidePassword": "隐藏密码", "or": "或以社媒账号登录", "emailRequired": "请输入邮箱", "passwordRequired": "请输入密码", "captchaInvalid": "验证码验证失败", "captchaError": "验证码验证出错"}, "register": {"title": "注册", "createAccount": "创建账号", "name": "姓名", "email": "邮箱", "password": "密码", "signUp": "注册", "signInHint": "已经有账号？登录", "checkEmail": "请检查您的邮箱", "showPassword": "显示密码", "hidePassword": "隐藏密码", "nameRequired": "请输入姓名", "emailRequired": "请输入邮箱", "passwordRequired": "请输入密码", "captchaInvalid": "验证码验证失败", "captchaError": "验证码验证出错"}, "forgotPassword": {"title": "忘记密码", "email": "邮箱", "send": "发送重置密码链接", "backToLogin": "返回登录", "checkEmail": "请检查您的邮箱", "emailRequired": "请输入邮箱"}, "resetPassword": {"title": "重置密码", "password": "密码", "reset": "重置密码", "backToLogin": "返回登录", "showPassword": "显示密码", "hidePassword": "隐藏密码", "minLength": "密码必须至少包含 8 个字符"}, "error": {"title": "哎呀！出错了！", "tryAgain": "请重试", "backToLogin": "返回登录", "checkEmail": "请检查您的邮箱"}, "common": {"termsOfService": "服务条款", "privacyPolicy": "隐私政策", "byClickingContinue": "继续即表示您同意我们的 ", "and": " 和 "}}, "BlogPage": {"title": "时尚科技博客", "description": "AI虚拟试衣技术、时尚趋势和电商创新的最新洞察", "subtitle": "来自时尚科技前沿的深度见解", "author": "作者", "categories": "分类", "tableOfContents": "目录", "readTime": "{minutes} 分钟阅读", "all": "全部", "noPostsFound": "没有找到文章", "allPosts": "全部文章", "morePosts": "更多文章"}, "DocsPage": {"toc": "目录", "search": "搜索文档", "lastUpdate": "最后更新于", "searchNoResult": "没有结果", "previousPage": "上一页", "nextPage": "下一页", "chooseLanguage": "选择语言", "title": "文档", "homepage": "首页", "blog": "博客"}, "Marketing": {"navbar": {"home": {"title": "首页"}, "features": {"title": "功能"}, "pricing": {"title": "价格"}, "blog": {"title": "博客"}, "outfit": {"title": "服装上身"}, "docs": {"title": "文档"}, "ai": {"title": "AI 工具", "items": {"text": {"title": "AI 文本", "description": "展示如何使用 AI 生成精彩文本"}, "image": {"title": "AI 图像", "description": "展示如何使用 AI 生成精美图像"}, "video": {"title": "AI 视频", "description": "展示如何使用 AI 生成惊人视频"}, "audio": {"title": "AI 音频", "description": "展示如何使用 AI 生成动听音频"}}}, "pages": {"title": "关于", "items": {"about": {"title": "关于我们", "description": "了解更多关于我们的公司、使命和价值观"}, "contact": {"title": "联系我们", "description": "与我们的团队联系，以获取支持或咨询"}, "waitlist": {"title": "邮件列表", "description": "加入我们的邮件列表，获取最新消息和更新"}, "changelog": {"title": "更新日志", "description": "查看我们产品的更新历史，查看最新动态"}, "roadmap": {"title": "路线图", "description": "探索我们的未来计划和即将推出的功能"}, "cookiePolicy": {"title": "<PERSON><PERSON> 政策", "description": "关于我们如何在网站上使用 Cookie 的信息"}, "privacyPolicy": {"title": "隐私政策", "description": "关于我们将如何保护和处理您在网站上的数据"}, "termsOfService": {"title": "服务条款", "description": "关于您与我们公司之间的法律协议和条款"}}}, "blocks": {"title": "内置组件", "items": {"magicui": {"title": "MagicUI 组件"}, "hero-section": {"title": "Hero 组件"}, "logo-cloud": {"title": "Logo Cloud 组件"}, "features": {"title": "Features 组件"}, "integrations": {"title": "Integrations 组件"}, "content": {"title": "Content 组件"}, "stats": {"title": "Stats 组件"}, "team": {"title": "Team 组件"}, "testimonials": {"title": "Testimonials 组件"}, "callToAction": {"title": "Call to Action 组件"}, "footer": {"title": "Footer 组件"}, "pricing": {"title": "Pricing 组件"}, "comparator": {"title": "Comparator 组件"}, "faq": {"title": "FAQ 组件"}, "login": {"title": "Login 组件"}, "signup": {"title": "Signup 组件"}, "forgot-password": {"title": "Forgot Password 组件"}, "contact": {"title": "Contact 组件"}}}}, "footer": {"tagline": "使用 绘盈 轻松处理电商运营", "product": {"title": "产品", "items": {"features": "功能", "pricing": "价格", "faq": "常见问题"}}, "resources": {"title": "资源", "items": {"blog": "博客", "docs": "文档", "changelog": "更新日志", "roadmap": "路线图"}}, "company": {"title": "公司", "items": {"about": "关于我们", "contact": "联系我们", "waitlist": "邮件列表"}}, "legal": {"title": "法律", "items": {"cookiePolicy": "<PERSON><PERSON>政策", "privacyPolicy": "隐私政策", "termsOfService": "服务条款"}}}, "avatar": {"dashboard": "工作台", "billing": "账单", "credits": "积分", "settings": "设置"}}, "Dashboard": {"dashboard": {"title": "仪表盘"}, "admin": {"title": "系统管理", "users": {"title": "用户管理", "fakeData": "注：只为演示功能，数据为假数据，封禁功能不可用", "error": "获取用户失败", "search": "搜索用户...", "columns": {"columns": "显示列", "name": "姓名", "email": "邮箱", "role": "角色", "createdAt": "创建时间", "customerId": "客户ID", "status": "状态", "banReason": "封禁原因", "banExpires": "封禁到期时间"}, "admin": "管理员", "user": "用户", "email": {"verified": "邮箱已验证", "unverified": "邮箱未验证"}, "emailCopied": "邮箱已复制到剪贴板", "banned": "账号被封禁", "active": "账号正常", "joined": "加入时间", "updated": "更新时间", "ban": {"reason": "封禁原因", "reasonPlaceholder": "请输入封禁该用户的原因", "defaultReason": "垃圾信息", "never": "永不解禁", "expires": "封禁到期时间", "selectDate": "选择日期", "button": "封禁用户", "success": "用户已被封禁", "error": "封禁用户失败"}, "unban": {"button": "解除封禁", "success": "用户已被解除封禁", "error": "解除封禁失败"}, "close": "关闭"}}, "settings": {"title": "设置", "profile": {"title": "账户", "description": "管理您的账户信息", "avatar": {"title": "头像", "description": "点击上传按钮上传自定义头像", "recommendation": "头像是可选的，但强烈推荐设置", "uploading": "上传中...", "uploadAvatar": "上传头像", "success": "头像更新成功", "fail": "更新头像失败"}, "name": {"title": "名字", "description": "请输入您的名字", "placeholder": "名字", "minLength": "请至少使用 3 个字符", "maxLength": "请最多使用 30 个字符", "hint": "请使用 3-30 个字符", "success": "名字更新成功", "fail": "更新名字失败", "saving": "保存中...", "save": "保存"}}, "billing": {"title": "账单", "description": "管理您的订阅和账单信息", "status": {"active": "已激活", "trial": "试用中", "free": "免费版", "lifetime": "终身版"}, "interval": {"month": "月", "year": "年", "oneTime": "一次性"}, "currentPlan": {"title": "当前方案", "description": "您当前的方案详情", "noPlan": "您没有激活的方案"}, "CustomerPortalButton": {"loading": "加载中...", "createCustomerPortalFailed": "打开Stripe客户界面失败"}, "price": "价格：", "periodStartDate": "周期开始日期：", "nextBillingDate": "下次账单日期：", "trialEnds": "试用结束日期：", "freePlanMessage": "您当前使用的是功能有限的免费方案", "lifetimeMessage": "您拥有所有高级功能的终身使用权限", "manageSubscription": "管理订阅和账单", "manageBilling": "管理账单", "upgradePlan": "升级方案", "retry": "重试", "errorMessage": "获取数据失败", "paymentSuccess": "支付成功"}, "credits": {"title": "积分", "description": "管理您的积分交易", "balance": {"title": "积分余额", "description": "您的积分余额", "credits": "积分", "creditsDescription": "您有 {credits} 积分", "creditsExpired": "积分已过期", "creditsAdded": "积分已添加到您的账户", "viewTransactions": "查看积分记录", "retry": "重试", "subscriptionCredits": "本月订阅获得 {credits} 积分", "lifetimeCredits": "本月终身会员获得 {credits} 积分", "expiringCredits": "{credits} 积分将在 {date} 过期"}, "packages": {"title": "积分套餐", "description": "购买积分以使用我们的更多服务", "purchase": "购买", "processing": "处理中...", "popular": "热门", "completePurchase": "请支付订单", "failedToFetchCredits": "获取积分失败", "failedToCreatePaymentIntent": "创建付款意向失败", "failedToInitiatePayment": "发起付款失败", "cancel": "取消", "purchaseFailed": "购买积分失败", "checkoutFailed": "创建支付会话失败", "loading": "加载中...", "pay": "支付"}, "transactions": {"title": "积分记录", "error": "获取积分交易记录失败", "search": "搜索积分交易记录...", "paymentIdCopied": "支付ID已复制到剪贴板", "columns": {"columns": "列", "id": "ID", "type": "类型", "description": "描述", "amount": "金额", "remainingAmount": "剩余金额", "paymentId": "支付编号", "expirationDate": "过期日期", "expirationDateProcessedAt": "过期处理时间", "createdAt": "创建时间", "updatedAt": "更新时间"}, "types": {"MONTHLY_REFRESH": "每月赠送", "REGISTER_GIFT": "注册赠送", "PURCHASE": "购买积分", "USAGE": "使用积分", "EXPIRE": "过期积分", "SUBSCRIPTION_RENEWAL": "订阅月度积分", "LIFETIME_MONTHLY": "终身月度积分"}, "detailViewer": {"title": "积分交易详情", "close": "关闭"}, "expired": "已过期", "never": "永不过期"}}, "notification": {"title": "通知", "description": "管理您的通知设置", "newsletter": {"title": "订阅", "description": "管理您的邮件列表订阅偏好", "label": "订阅邮件列表", "hint": "您可以随时更改订阅偏好", "emailRequired": "订阅邮件列表需要邮箱", "subscribeSuccess": "成功订阅邮件列表", "subscribeFail": "订阅邮件列表失败", "unsubscribeSuccess": "成功取消订阅邮件列表", "unsubscribeFail": "取消订阅邮件列表失败", "error": "更新订阅时发生错误"}}, "security": {"title": "安全", "description": "管理您的安全设置", "updatePassword": {"title": "修改密码", "description": "输入您的当前密码和新密码", "currentPassword": "当前密码", "currentRequired": "当前密码是必填项", "newPassword": "新密码", "newMinLength": "密码必须至少包含 8 个字符", "hint": "请至少使用 8 个字符作为密码", "showPassword": "显示密码", "hidePassword": "隐藏密码", "success": "密码更新成功", "fail": "更新密码失败", "saving": "保存中...", "save": "保存"}, "resetPassword": {"title": "重置密码", "description": "重置密码以启用邮箱登录", "info": "重置密码将允许您除了社交登录方式外，还可以使用邮箱和密码登录，您将收到一封包含重置密码链接的电子邮件", "button": "重置密码"}, "deleteAccount": {"title": "删除账号", "description": "永久删除您的账号和所有内容", "warning": "此操作是不可逆的，请谨慎操作", "button": "删除账号", "confirmTitle": "删除账号", "confirmDescription": "您确定要删除您的账号吗？此操作无法撤销", "confirm": "删除", "cancel": "取消", "deleting": "删除中...", "success": "账号删除成功", "fail": "删除账号失败"}}}, "upgrade": {"title": "升级", "description": "升级到Pro以获取所有功能", "button": "升级"}}, "Mail": {"common": {"team": "{name} 团队", "copyright": "©️ {year} 版权所有。"}, "verifyEmail": {"title": "你好，{name}。", "body": "请点击下面的链接验证您的邮箱地址。", "confirmEmail": "确认邮箱", "subject": "验证您的邮箱"}, "forgotPassword": {"title": "你好，{name}。", "body": "请点击下面的链接重置您的密码。", "resetPassword": "重置密码", "subject": "重置您的密码"}, "subscribeNewsletter": {"body": "感谢您订阅我们的邮件列表，我们将为您提供最新的新闻和更新。", "subject": "感谢您的订阅"}, "contactMessage": {"name": "姓名: {name}", "email": "邮箱: {email}", "message": "消息: {message}", "subject": "来自网站的联系消息"}}, "HomePage": {"title": "<PERSON><PERSON>", "description": "体验AI驱动的虚拟试衣技术，开启时尚购物新时代", "hero": {"title": "世界买家触手可及", "description": "运用前沿AI虚拟试衣技术革新在线购物体验。在购买前真实预览服装效果，享受逼真准确的虚拟试穿体验。", "introduction": "介绍AI虚拟试衣", "primary": "立即体验", "secondary": "观看演示"}, "logocloud": {"title": "全球领先时尚品牌的信赖之选"}, "integration": {"title": "AI技术架构", "subtitle": "前沿AI技术驱动", "description": "我们的虚拟试衣平台运用最新的计算机视觉、机器学习和3D建模技术。", "learnMore": "了解更多", "items": {"item-1": {"title": "计算机视觉AI", "description": "先进的人体检测和姿态估计技术，实现精准的虚拟试衣效果。"}, "item-2": {"title": "3D建模引擎", "description": "实时3D服装仿真，基于物理的布料行为模拟。"}, "item-3": {"title": "机器学习", "description": "持续学习算法，不断提升试衣准确度和用户体验。"}, "item-4": {"title": "AR增强现实", "description": "增强现实技术集成，提供沉浸式试衣体验。"}, "item-5": {"title": "云端处理", "description": "高性能云基础设施，实现即时虚拟试衣结果。"}, "item-6": {"title": "移动端优化", "description": "针对移动设备优化，提供无缝跨平台体验。"}}}, "integration2": {"title": "无缝电商平台集成", "description": "通过我们易于使用的API，将Huiying的AI虚拟试衣技术集成到您现有的电商平台中。", "primaryButton": "开始集成", "secondaryButton": "查看API文档"}, "features": {"title": "AI驱动功能", "subtitle": "革命性虚拟试衣技术", "description": "体验未来在线时尚购物，我们的前沿AI功能让虚拟试衣体验尽可能真实。", "items": {"item-1": {"title": "逼真虚拟试穿", "description": "我们的先进AI创造照片级真实的虚拟试衣体验，准确展示服装在您身上的外观和合身度，充分考虑您独特的体型、尺寸和比例。"}, "item-2": {"title": "智能尺码推荐", "description": "AI驱动的尺码推荐引擎分析您的身体测量数据和合身偏好，为每件服装推荐完美尺码，减少退货并提高客户满意度。"}, "item-3": {"title": "360°虚拟衣橱", "description": "通过我们的360度虚拟衣橱功能，从各个角度查看您穿着任何服装的效果。混搭不同单品创造完整造型，看看它们如何搭配。"}, "item-4": {"title": "实时风格匹配", "description": "我们的AI分析您的风格偏好，推荐符合您品味的互补单品。发现新造型，获得专为您个人风格量身定制的完整搭配。"}}}, "features2": {"title": "高级功能", "subtitle": "下一代虚拟试衣功能", "description": "探索让Huiying成为最先进虚拟试衣平台的高级AI功能。", "feature-1": "体型分析", "feature-2": "面料物理仿真", "feature-3": "光照适应", "feature-4": "多设备同步"}, "features3": {"title": "完整解决方案", "subtitle": "虚拟试衣所需的一切功能", "description": "Huiying提供全面的AI驱动工具和功能套件，改变您的时尚电商体验。", "items": {"item-1": {"title": "即时虚拟试衣", "description": "上传照片，通过我们的实时AI处理，即刻查看任何服装在您身上的效果。"}, "item-2": {"title": "身体测量AI", "description": "使用先进的计算机视觉算法，从单张照片中提取准确的身体测量数据。"}, "item-3": {"title": "合身度预测引擎", "description": "使用我们专有的合身度分析技术，在购买前预测服装的合身程度。"}, "item-4": {"title": "风格推荐", "description": "基于您的偏好、体型和时尚趋势，获得个性化的风格建议。"}, "item-5": {"title": "虚拟衣橱", "description": "构建和管理您的虚拟衣橱，混搭单品，规划完整造型。"}, "item-6": {"title": "社交分享", "description": "与朋友分享您的虚拟试衣结果，在做出购买决定前获得反馈。"}}}, "pricing": {"title": "定价方案", "subtitle": "电商运营解决方案", "description": "选择最适合您业务需求的方案，开始改变客户的购物体验"}, "faqs": {"title": "常见问题", "subtitle": "关于AI虚拟试衣的常见问题解答", "items": {"item-1": {"question": "AI虚拟试衣的准确度如何？", "answer": "我们的AI虚拟试衣技术在合身度预测和真实感可视化方面达到95%+的准确率，并通过机器学习持续改进。"}, "item-2": {"question": "可以虚拟试穿哪些类型的服装？", "answer": "Huiying支持所有主要服装类别，包括上衣、连衣裙、裤子、外套和配饰。我们正在不断扩展支持的商品类型。"}, "item-3": {"question": "使用虚拟试衣需要特殊设备吗？", "answer": "不需要特殊设备！只需使用您的智能手机摄像头或上传照片。我们的AI可以处理标准照片来创造逼真的试衣体验。"}, "item-4": {"question": "尺码推荐是如何工作的？", "answer": "我们的AI从照片中分析您的身体测量数据，并与服装规格进行比较，推荐最佳尺码，可减少高达40%的退货率。"}, "item-5": {"question": "可以将Huiying集成到我的电商店铺吗？", "answer": "可以！我们为主要电商平台提供易于集成的API和插件。请联系我们的团队获取集成支持。"}}}, "testimonials": {"title": "客户成功案例", "subtitle": "时尚零售商对Huiying的评价", "items": {"item-1": {"name": "陈莎拉", "role": "电商总监", "image": "https://randomuser.me/api/portraits/women/1.jpg", "quote": "Huiying的AI虚拟试衣让我们的转化率提升了35%，退货率降低了40%。这是在线时尚零售的游戏规则改变者。"}, "item-2": {"name": "马库斯·罗德里格斯", "role": "时尚品牌创始人", "image": "https://randomuser.me/api/portraits/men/6.jpg", "quote": "虚拟试衣体验如此逼真，客户对在线购买充满信心。我们的客户满意度评分从未如此之高。"}, "item-3": {"name": "艾米丽·沃森", "role": "数字营销经理", "image": "https://randomuser.me/api/portraits/women/7.jpg", "quote": "集成过程非常顺畅，效果立竿见影。客户喜欢在购买前能够看到服装的效果。"}, "item-4": {"name": "Anonymous author", "role": "产品经理", "image": "https://randomuser.me/api/portraits/men/8.jpg", "quote": "我不熟悉 Tailwind，我想自己做一些页面，我在网上搜索了很多英雄页面和区块。然而，大多数都没有给我一个清晰的想法，或者需要一些 HTML/CSS 编码背景来从原始文件中做一些更改，或者太贵了。 它非常容易理解，你可以在开始时修改代码/区块以完美地适应你的页面目的。"}, "item-5": {"name": "<PERSON><PERSON><PERSON>", "role": "高级软件工程师", "image": "https://randomuser.me/api/portraits/men/4.jpg", "quote": "正在重新定义网页设计标准，这些区块为那些喜欢美丽但可能缺乏时间实现它的人提供了简单且高效的方式。我只能推荐这个不可思议的奇迹。"}, "item-6": {"name": "<PERSON><PERSON> Fred", "role": "全栈开发工程师", "image": "https://randomuser.me/api/portraits/men/2.jpg", "quote": "组件区块设计精美且易于使用，使创建一个出色的网站变得轻而易举。"}, "item-7": {"name": "<PERSON><PERSON><PERSON>", "role": "ChatExtend 创始人", "image": "https://randomuser.me/api/portraits/men/5.jpg", "quote": "解锁了一个秘密的设计超能力。它是简单性和多功能性的完美融合，使我们能够创建既令人惊叹又用户友好的界面。"}, "item-8": {"name": "<PERSON>", "role": "全栈开发工程师", "image": "https://randomuser.me/api/portraits/men/9.jpg", "quote": "改变了我的网页开发方式。他们的 UI 组件、区块和模板极大地加速了我的工作流程。定制每个方面的灵活性使我能够创建独特的用户体验。"}, "item-9": {"name": "<PERSON><PERSON><PERSON>", "role": "MerakiUI 创建者", "image": "https://randomuser.me/api/portraits/men/10.jpg", "quote": "一个优雅、干净且响应式的 SaaS 模板，它非常有助于快速开始您的项目。"}, "item-10": {"name": "<PERSON>", "role": "TailwindAwesome 创建者", "image": "https://randomuser.me/api/portraits/men/11.jpg", "quote": "这些组件区块结构良好，易于使用，设计精美。它使创建一个出色的网站变得非常容易。"}, "item-11": {"name": "<PERSON>", "role": "Google 开发者专家", "image": "https://randomuser.me/api/portraits/men/12.jpg", "quote": "模板是任何想要创建一个美丽且功能齐全的网站但没有网页设计经验的人的完美解决方案。这些模板易于使用，可定制，并且响应迅速。"}, "item-12": {"name": "<PERSON>", "role": "软件工程师", "image": "https://randomuser.me/api/portraits/men/13.jpg", "quote": "设计得如此出色，即使没有网页设计经验，您也可以创造奇迹。让自己被吸引！"}}}, "stats": {"title": "影响力统计", "subtitle": "Huiying的实证成果", "description": "看看Huiying的AI虚拟试衣技术如何在全球范围内改变时尚电商", "items": {"item-1": {"title": "服务时尚品牌"}, "item-2": {"title": "每日虚拟试衣次数"}, "item-3": {"title": "退货率降低"}}}, "calltoaction": {"title": "改变您的时尚业务", "description": "加入使用Huiying AI虚拟试衣技术的领先时尚品牌，提升销量并减少退货", "primaryButton": "开始免费试用", "secondaryButton": "预约演示"}}, "AITextPage": {"title": "AI 文本", "description": "使用 AI 分析网页内容，提取关键信息、功能和见解", "content": "网页内容分析器", "subtitle": "输入网站 URL，使用 AI 分析其内容", "analyzer": {"title": "网页内容分析器", "description": "使用 AI 分析任何网站的内容，提取结构化信息", "placeholder": "输入网站 URL（例如：https://example.com）", "button": "分析网站", "loading": {"scraping": "正在抓取网站内容...", "analyzing": "正在使用 AI 分析内容..."}, "results": {"title": "分析结果", "newAnalysis": "分析其他网站", "sections": {"title": "标题", "description": "描述", "introduction": "介绍", "features": "功能", "pricing": "定价", "useCases": "使用场景", "screenshot": "网站截图"}}, "errors": {"invalidUrl": "请输入以 http:// 或 https:// 开头的有效 URL", "analysisError": "分析网站失败，请重试。", "networkError": "网络错误，请检查您的连接并重试。", "insufficientCredits": "积分不足，请购买更多积分以继续。"}}, "features": {"scraping": {"title": "智能网页抓取", "description": "先进的网页抓取技术从任何网站提取干净、结构化的内容"}, "analysis": {"title": "AI 驱动分析", "description": "智能 AI 分析提取关键见解、功能和结构化信息"}, "results": {"title": "结构化结果", "description": "获得有组织、易于阅读的结果，包含清晰的部分和可操作的见解"}}}, "AIImagePage": {"title": "AI虚拟试衣", "description": "体验由先进AI图像处理技术驱动的逼真虚拟服装试穿", "content": "虚拟试衣工作室"}, "AIVideoPage": {"title": "AI时尚视频", "description": "使用AI驱动的虚拟试衣和风格推荐创建动态时尚视频", "content": "时尚视频创作器"}, "AIAudioPage": {"title": "AI风格助手", "description": "从我们的AI语音助手获得个性化时尚建议和搭配技巧", "content": "语音风格助手"}}