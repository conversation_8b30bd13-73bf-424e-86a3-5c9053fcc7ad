{"Metadata": {"name": "<PERSON><PERSON>", "title": "Huiying - AI Virtual Try-On Platform", "description": "Experience the future of fashion with <PERSON><PERSON>'s AI-powered virtual try-on technology. Try on clothes virtually with stunning realism."}, "Common": {"login": "Log in", "logout": "Log out", "signUp": "Sign up", "language": "Switch language", "mode": {"label": "Toggle mode", "light": "Light", "dark": "Dark", "system": "System"}, "theme": {"label": "Toggle theme", "default": "<PERSON><PERSON><PERSON>", "blue": "Blue", "green": "Green", "amber": "Amber", "neutral": "Neutral"}, "copy": "Copy", "saving": "Saving...", "save": "Save", "loading": "Loading...", "cancel": "Cancel", "logoutFailed": "Failed to log out", "table": {"totalRecords": "Total {count} records", "noResults": "No results", "loading": "Loading...", "columns": "Columns", "rowsPerPage": "Rows per page", "page": "Page", "firstPage": "First Page", "lastPage": "Last Page", "nextPage": "Next Page", "previousPage": "Previous Page", "ascending": "Asc", "descending": "Desc"}}, "PricingPage": {"title": "AI Virtual Try-On Pricing", "description": "Choose the perfect plan to integrate Huiying's AI virtual try-on technology into your fashion business", "subtitle": "Flexible pricing plans for every business size", "monthly": "Monthly", "yearly": "Yearly", "PricingCard": {"freePrice": "$0", "perMonth": "/month", "perYear": "/year", "popular": "Popular", "currentPlan": "Current Plan", "yourCurrentPlan": "Your Current Plan", "getStartedForFree": "Get Started For Free", "getLifetimeAccess": "Get Lifetime Access", "getStarted": "Get Started", "notAvailable": "Not Available", "daysTrial": "{days}-day free trial"}, "CheckoutButton": {"loading": "Loading...", "checkoutFailed": "Failed to open checkout page"}}, "PricePlans": {"free": {"name": "Free", "description": "Basic features for personal use", "features": {"feature-1": "Up to 3 projects", "feature-2": "1 GB storage", "feature-3": "Basic analytics", "feature-4": "Community support"}, "limits": {"limit-1": "Custom domains", "limit-2": "Custom branding", "limit-3": "Lifetime updates"}}, "pro": {"name": "Pro", "description": "Advanced features for professionals", "features": {"feature-1": "Unlimited projects", "feature-2": "10 GB storage", "feature-3": "Advanced analytics", "feature-4": "Priority support", "feature-5": "Custom domains"}, "limits": {"limit-1": "Custom branding", "limit-2": "Lifetime updates"}}, "lifetime": {"name": "Lifetime", "description": "Premium features with one-time payment", "features": {"feature-1": "All Pro features", "feature-2": "100 GB storage", "feature-3": "Dedicated support", "feature-4": "Enterprise-grade security", "feature-5": "Advanced integrations", "feature-6": "Custom branding", "feature-7": "Lifetime updates"}}}, "CreditPackages": {"basic": {"name": "Basic", "description": "Basic credits package description"}, "standard": {"name": "Standard", "description": "Standard credits package description"}, "premium": {"name": "Premium", "description": "Premium credits package description"}, "enterprise": {"name": "Enterprise", "description": "Enterprise credits package description"}}, "NotFoundPage": {"title": "404", "message": "Sorry, the page you are looking for does not exist.", "backToHome": "Back to home"}, "ErrorPage": {"title": "Oops! Something went wrong!", "tryAgain": "Try again", "backToHome": "Back to home"}, "AboutPage": {"title": "About", "description": "Huiying revolutionizes fashion retail with cutting-edge AI virtual try-on technology, making online shopping more engaging and confident.", "authorName": "<PERSON><PERSON>", "authorBio": "AI Virtual Try-On Platform", "introduction": "Huiying is the leading AI-powered virtual try-on platform that transforms how people shop for clothes online. Using advanced computer vision and machine learning, we create realistic virtual fitting experiences. If you have any questions, welcome to contact us.", "talkWithMe": "Contact Us", "followMe": "Follow us on X"}, "OutfitPage": {"title": "Outfit Gallery", "description": "Discover perfect outfit combinations with AI-powered virtual try-on technology. <PERSON><PERSON><PERSON> curated styles and see how they look on you.", "uploadPhoto": "Upload Photo", "tryDemo": "Try Demo", "features": {"upload": {"title": "Upload Photo", "description": "Upload your photo and our AI will analyze the best outfit combinations for you"}, "ai": {"title": "AI Smart Matching", "description": "Advanced AI algorithms analyze your body type and style to recommend the most suitable outfit combinations"}, "result": {"title": "Preview Results", "description": "Real-time preview of how outfits look on you, so you can see the perfect match before purchasing"}}, "gallery": {"title": "Style Gallery", "subtitle": "Discover Your Perfect Look", "description": "Browse our curated collection of outfit combinations and see how they look with AI virtual try-on", "filters": {"all": "All", "male": "Men", "female": "Women"}, "buttons": {"buyNow": "Buy Now", "tryOn": "Try On"}, "card": {"item": "item", "items": "items", "genderTypes": {"male": "Men", "female": "Women"}}, "pagination": {"previous": "Previous", "next": "Next"}, "modal": {"title": "Outfit Details", "close": "Close"}}, "comingSoon": {"title": "Coming Soon", "description": "We are working hard to develop this feature. Stay tuned! You can subscribe to our notifications to be the first to know when it goes live.", "notify": "Subscribe for Updates"}}, "ChangelogPage": {"title": "Changelog", "description": "Stay up to date with the latest changes in our product", "subtitle": "Stay up to date with the latest changes in our product"}, "ContactPage": {"title": "Contact", "description": "We'll help you find the right plan for your business", "subtitle": "We'll help you find the right plan for your business", "form": {"title": "Contact Us", "description": "If you have any questions or feedback, please reach out to our team", "name": "Name", "email": "Email", "message": "Message", "submit": "Submit", "submitting": "Submitting...", "success": "Message sent successfully", "fail": "Failed to send message", "nameMinLength": "Name must be at least 3 characters", "nameMaxLength": "Name must not exceed 30 characters", "emailValidation": "Please enter a valid email address", "messageMinLength": "Message must be at least 10 characters", "messageMaxLength": "Message must not exceed 500 characters"}}, "WaitlistPage": {"title": "Waitlist", "description": "Join our waitlist for the launch of our product", "subtitle": "Join our waitlist for the launch of our product", "form": {"title": "Join Our Waitlist", "description": "We will notify you when we launch our product", "email": "Email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "success": "Subscribed successfully", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "Newsletter": {"title": "Newsletter", "subtitle": "Join the community", "description": "Subscribe to our newsletter for the latest news and updates", "form": {"email": "Email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "success": "Subscribed successfully", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "AuthPage": {"login": {"title": "<PERSON><PERSON>", "welcomeBack": "Welcome back", "email": "Email", "password": "Password", "signIn": "Sign In", "signUpHint": "Don't have an account? Sign up", "forgotPassword": "Forgot Password?", "signInWithGoogle": "Sign In with Google", "signInWithGitHub": "Sign In with GitHub", "showPassword": "Show password", "hidePassword": "Hide password", "or": "Or continue with", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password", "captchaInvalid": "Captcha verification failed", "captchaError": "Captcha verification error"}, "register": {"title": "Register", "createAccount": "Create an account", "name": "Name", "email": "Email", "password": "Password", "signUp": "Sign Up", "signInHint": "Already have an account? Sign in", "checkEmail": "Please check your email inbox", "showPassword": "Show password", "hidePassword": "Hide password", "nameRequired": "Please enter your name", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password", "captchaInvalid": "Captcha verification failed", "captchaError": "Captcha verification error"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "send": "Send reset link", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox", "emailRequired": "Please enter your email"}, "resetPassword": {"title": "Reset Password", "password": "Password", "reset": "Reset password", "backToLogin": "Back to login", "showPassword": "Show password", "hidePassword": "Hide password", "minLength": "Password must be at least 8 characters"}, "error": {"title": "Oops! Something went wrong!", "tryAgain": "Please try again.", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox"}, "common": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "byClickingContinue": "By clicking continue, you agree to our ", "and": " and "}}, "BlogPage": {"title": "Fashion Tech Blog", "description": "Latest insights on AI virtual try-on technology, fashion trends, and e-commerce innovation", "subtitle": "Insights from the future of fashion technology", "author": "Author", "categories": "Categories", "tableOfContents": "Table of Contents", "readTime": "{minutes} min read", "all": "All", "noPostsFound": "No posts found", "allPosts": "All Posts", "morePosts": "More Posts"}, "DocsPage": {"toc": "Table of Contents", "search": "Search docs", "lastUpdate": "Last updated on", "searchNoResult": "No results", "previousPage": "Previous", "nextPage": "Next", "chooseLanguage": "Select language", "title": "Docs", "homepage": "Homepage", "blog": "Blog"}, "Marketing": {"navbar": {"home": {"title": "Home"}, "features": {"title": "Features"}, "pricing": {"title": "Pricing"}, "blog": {"title": "Blog"}, "outfit": {"title": "Outfix"}, "docs": {"title": "Docs"}, "ai": {"title": "AI Tools", "items": {"text": {"title": "AI Text", "description": "Show how to use AI to write stunning text"}, "image": {"title": "AI Image", "description": "Show how to use AI to generate beautiful images"}, "video": {"title": "AI Video", "description": "Show how to use AI to generate amazing videos"}, "audio": {"title": "AI Audio", "description": "Show how to use AI to generate wonderful audio"}}}, "pages": {"title": "About", "items": {"about": {"title": "About", "description": "Learn more about our company, mission, and values"}, "contact": {"title": "Contact", "description": "Get in touch with our team for support or inquiries"}, "waitlist": {"title": "Waitlist", "description": "Join our waitlist for latest news and updates"}, "changelog": {"title": "Changelog", "description": "See the latest updates to our products"}, "roadmap": {"title": "Roadmap", "description": "Explore our future plans and upcoming features"}, "cookiePolicy": {"title": "<PERSON><PERSON>", "description": "How we use the cookies on our website"}, "privacyPolicy": {"title": "Privacy Policy", "description": "How we protect and handle your data"}, "termsOfService": {"title": "Terms of Service", "description": "The legal agreement between you and our company"}}}, "blocks": {"title": "Blocks", "items": {"magicui": {"title": "MagicUI Blocks"}, "hero-section": {"title": "Hero Blocks"}, "logo-cloud": {"title": "Logo Cloud Blocks"}, "integrations": {"title": "Integrations Blocks"}, "features": {"title": "Features Blocks"}, "content": {"title": "Content Blocks"}, "stats": {"title": "Stats Blocks"}, "team": {"title": "Team Blocks"}, "testimonials": {"title": "Testimonials Blocks"}, "callToAction": {"title": "Call to Action Blocks"}, "footer": {"title": "Footer Blocks"}, "pricing": {"title": "Pricing Blocks"}, "comparator": {"title": "Comparator Blocks"}, "faq": {"title": "FAQ Blocks"}, "login": {"title": "Login Blocks"}, "signup": {"title": "Signup Blocks"}, "forgot-password": {"title": "Forgot Password Blocks"}, "contact": {"title": "Contact Blocks"}}}}, "footer": {"tagline": "Make AI SaaS in days, simply and effortlessly", "product": {"title": "Product", "items": {"features": "Features", "pricing": "Pricing", "faq": "FAQ"}}, "resources": {"title": "Resources", "items": {"blog": "Blog", "docs": "Documentation", "changelog": "Changelog", "roadmap": "Roadmap"}}, "company": {"title": "Company", "items": {"about": "About", "contact": "Contact", "waitlist": "Waitlist"}}, "legal": {"title": "Legal", "items": {"cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}}}, "avatar": {"dashboard": "Dashboard", "billing": "Billing", "credits": "Credits", "settings": "Settings"}}, "Dashboard": {"dashboard": {"title": "Dashboard"}, "admin": {"title": "Admin", "users": {"title": "Users", "fakeData": "Note: Faked data for demonstration, some features are disabled", "error": "Failed to get users", "search": "Search users...", "columns": {"columns": "Columns", "name": "Name", "email": "Email", "role": "Role", "createdAt": "Created At", "customerId": "Customer ID", "status": "Status", "banReason": "Ban Reason", "banExpires": "Ban Expires"}, "admin": "Admin", "user": "User", "email": {"verified": "<PERSON><PERSON>", "unverified": "Email Unverified"}, "emailCopied": "Email copied to clipboard", "banned": "Banned", "active": "Active", "joined": "Joined at", "updated": "Updated at", "ban": {"reason": "Ban Reason", "reasonPlaceholder": "Enter the reason for banning this user", "defaultReason": "Spamming", "never": "Never", "expires": "Ban Expires", "selectDate": "Select Date", "button": "Ban User", "success": "User has been banned", "error": "Failed to ban user"}, "unban": {"button": "Unban User", "success": "User has been unbanned", "error": "Failed to unban user"}, "close": "Close"}}, "settings": {"title": "Settings", "profile": {"title": "Profile", "description": "Manage your account information", "avatar": {"title": "Avatar", "description": "Click upload button to upload a custom one", "recommendation": "An avatar is optional but strongly recommended", "uploading": "Uploading...", "uploadAvatar": "Upload Avatar", "success": "Avatar updated successfully", "fail": "Failed to update avatar"}, "name": {"title": "Name", "description": "Please enter your display name", "placeholder": "Enter your name", "minLength": "Please use 3 characters at minimum", "maxLength": "Please use 30 characters at maximum", "hint": "Please use 3-30 characters for your name", "success": "Name updated successfully", "fail": "Failed to update name", "saving": "Saving...", "save": "Save"}}, "billing": {"title": "Billing", "description": "Manage your subscription and billing details", "status": {"active": "Active", "trial": "Trial", "free": "Free", "lifetime": "Lifetime"}, "interval": {"month": "month", "year": "year", "oneTime": "one-time"}, "currentPlan": {"title": "Current Plan", "description": "Your current plan details", "noPlan": "You have no active plan"}, "CustomerPortalButton": {"loading": "Loading...", "createCustomerPortalFailed": "Failed to open Stripe customer portal"}, "price": "Price:", "periodStartDate": "Period start date:", "nextBillingDate": "Next billing date:", "trialEnds": "Trial ends:", "freePlanMessage": "You are currently on the free plan with limited features", "lifetimeMessage": "You have lifetime access to all premium features", "manageSubscription": "Manage Subscription and Billing", "manageBilling": "Manage Billing", "upgradePlan": "Upgrade Plan", "retry": "Retry", "errorMessage": "Failed to get data", "paymentSuccess": "Payment successful"}, "credits": {"title": "Credits", "description": "Manage your credit transactions", "balance": {"title": "Credit Balance", "description": "Your credit balance", "credits": "Credits", "creditsDescription": "You have {credits} credits", "creditsExpired": "Credits expired", "creditsAdded": "Credits have been added to your account", "viewTransactions": "View Credit Transactions", "retry": "Retry", "subscriptionCredits": "{credits} credits from subscription this month", "lifetimeCredits": "{credits} credits from lifetime plan this month", "expiringCredits": "{credits} credits expiring on {date}"}, "packages": {"title": "Credit Packages", "description": "Purchase additional credits to use our services", "purchase": "Purchase", "processing": "Processing...", "popular": "Popular", "completePurchase": "Complete Your Purchase", "failedToFetchCredits": "Failed to fetch credits", "failedToCreatePaymentIntent": "Failed to create payment intent", "failedToInitiatePayment": "Failed to initiate payment", "cancel": "Cancel", "purchaseFailed": "Purchase credits failed", "checkoutFailed": "Failed to create checkout session", "loading": "Loading...", "pay": "Pay"}, "transactions": {"title": "Credit Transactions", "error": "Failed to get credit transactions", "search": "Search credit transactions...", "paymentIdCopied": "Payment ID copied to clipboard", "columns": {"columns": "Columns", "id": "ID", "type": "Type", "description": "Description", "amount": "Amount", "remainingAmount": "Remaining Amount", "paymentId": "Payment ID", "expirationDate": "Expiration Date", "expirationDateProcessedAt": "Expiration Date Processed At", "createdAt": "Created At", "updatedAt": "Updated At"}, "types": {"MONTHLY_REFRESH": "Monthly Refresh", "REGISTER_GIFT": "Register Gift", "PURCHASE": "Purchased Credits", "USAGE": "Consumed Credits", "EXPIRE": "Expired Credits", "SUBSCRIPTION_RENEWAL": "Subscription Renewal", "LIFETIME_MONTHLY": "Lifetime Monthly"}, "detailViewer": {"title": "Credit Transaction Detail", "close": "Close"}, "expired": "Expired", "never": "Never"}}, "notification": {"title": "Notification", "description": "Manage your notification preferences", "newsletter": {"title": "Newsletter Subscription", "description": "Manage your newsletter subscription preferences", "label": "Subscribe to newsletter", "hint": "You can change your subscription preferences at any time", "emailRequired": "Email is required to subscribe to the newsletter", "subscribeSuccess": "Successfully subscribed to the newsletter", "subscribeFail": "Failed to subscribe to the newsletter", "unsubscribeSuccess": "Successfully unsubscribed from the newsletter", "unsubscribeFail": "Failed to unsubscribe from the newsletter", "error": "An error occurred while updating your subscription"}}, "security": {"title": "Security", "description": "Manage your security settings", "updatePassword": {"title": "Change Password", "description": "Enter your current password and a new password", "currentPassword": "Current Password", "currentRequired": "Current password is required", "newPassword": "New Password", "newMinLength": "Password must be at least 8 characters", "hint": "Please use at least 8 characters for password", "showPassword": "Show password", "hidePassword": "Hide password", "success": "Password updated successfully", "fail": "Failed to update password", "saving": "Saving...", "save": "Save"}, "resetPassword": {"title": "Reset Password", "description": "Reset password to enable email login", "info": "Resetting your password will allow you to sign in using your email and password in addition to your social login methods. You will receive an email with instructions to reset your password", "button": "Reset Password"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently remove your account and all of its contents", "warning": "This action is not reversible, so please continue with caution", "button": "Delete Account", "confirmTitle": "Delete Account", "confirmDescription": "Are you sure you want to delete your account? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Account deleted successfully", "fail": "Failed to delete account"}}}, "upgrade": {"title": "Upgrade", "description": "Upgrade to Pro to access all features", "button": "Upgrade"}}, "Mail": {"common": {"team": "{name} Team", "copyright": "©️ {year} All Rights Reserved."}, "verifyEmail": {"title": "Hi, {name}.", "body": "Please click the link below to verify your email address.", "confirmEmail": "Confirm email", "subject": "Verify your email"}, "forgotPassword": {"title": "Hi, {name}.", "body": "Please click the link below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "subscribeNewsletter": {"body": "Thank you for subscribing to the newsletter. We will keep you updated with the latest news and updates.", "subject": "Thanks for subscribing"}, "contactMessage": {"name": "Name: {name}", "email": "Email: {email}", "message": "Message: {message}", "subject": "Contact Message from Website"}}, "HomePage": {"title": "<PERSON><PERSON>", "description": "Experience the future of fashion with AI-powered virtual try-on technology", "hero": {"title": "Try On Clothes Virtually with AI-Powered Precision", "description": "Huiying revolutionizes online shopping with cutting-edge AI virtual try-on technology. See how clothes look on you before you buy, with stunning realism and accuracy.", "introduction": "Introducing Huiying AI Virtual Try-On", "primary": "Try It Now", "secondary": "Watch Demo"}, "logocloud": {"title": "Trusted by leading fashion brands worldwide"}, "integration": {"title": "AI Technology Stack", "subtitle": "Powered by cutting-edge AI technologies", "description": "Our virtual try-on platform leverages the latest advances in computer vision, machine learning, and 3D modeling.", "learnMore": "Learn More", "items": {"item-1": {"title": "Computer Vision AI", "description": "Advanced body detection and pose estimation for accurate virtual fitting."}, "item-2": {"title": "3D Modeling Engine", "description": "Real-time 3D garment simulation with physics-based cloth behavior."}, "item-3": {"title": "Machine Learning", "description": "Continuous learning algorithms that improve fit accuracy over time."}, "item-4": {"title": "AR Technology", "description": "Augmented reality integration for immersive try-on experiences."}, "item-5": {"title": "Cloud Processing", "description": "High-performance cloud infrastructure for instant virtual try-on results."}, "item-6": {"title": "Mobile Optimization", "description": "Optimized for mobile devices with seamless cross-platform experience."}}}, "integration2": {"title": "Seamless E-commerce Integration", "description": "Integrate Huiying's AI virtual try-on technology into your existing e-commerce platform with our easy-to-use APIs.", "primaryButton": "Start Integration", "secondaryButton": "View API Docs"}, "features": {"title": "AI-Powered Features", "subtitle": "Revolutionary virtual try-on technology", "description": "Experience the future of online fashion shopping with our cutting-edge AI features designed to make virtual try-on as realistic as possible.", "items": {"item-1": {"title": "Realistic Virtual Fitting", "description": "Our advanced AI creates photorealistic virtual try-on experiences that accurately show how clothes will look and fit on your body, taking into account your unique body shape, size, and proportions."}, "item-2": {"title": "Smart Size Recommendation", "description": "AI-powered size recommendation engine analyzes your body measurements and fit preferences to suggest the perfect size for each garment, reducing returns and increasing customer satisfaction."}, "item-3": {"title": "360° Virtual Wardrobe", "description": "View yourself in any outfit from every angle with our 360-degree virtual wardrobe feature. Mix and match different pieces to create complete looks and see how they work together."}, "item-4": {"title": "Real-time Style Matching", "description": "Our AI analyzes your style preferences and suggests complementary items that match your taste. Discover new looks and complete outfits tailored specifically to your personal style."}}}, "features2": {"title": "Advanced Capabilities", "subtitle": "Next-generation virtual try-on features", "description": "Discover the advanced AI capabilities that make Huiying the most sophisticated virtual try-on platform available.", "feature-1": "Body Shape Analysis", "feature-2": "Fabric Physics Simulation", "feature-3": "Lighting Adaptation", "feature-4": "Multi-Device Sync"}, "features3": {"title": "Complete Solution", "subtitle": "Everything you need for virtual try-on", "description": "Huiying provides a comprehensive suite of AI-powered tools and features to transform your fashion e-commerce experience.", "items": {"item-1": {"title": "Instant Virtual Try-On", "description": "Upload a photo and instantly see how any garment looks on you with our real-time AI processing."}, "item-2": {"title": "Body Measurement AI", "description": "Accurate body measurements extracted from a single photo using advanced computer vision algorithms."}, "item-3": {"title": "Fit Prediction Engine", "description": "Predict how well clothes will fit before purchase using our proprietary fit analysis technology."}, "item-4": {"title": "Style Recommendation", "description": "Get personalized style suggestions based on your preferences, body type, and fashion trends."}, "item-5": {"title": "Virtual Wardrobe", "description": "Build and manage your virtual wardrobe, mix and match items, and plan complete outfits."}, "item-6": {"title": "Social Sharing", "description": "Share your virtual try-on results with friends and get feedback before making purchase decisions."}}}, "pricing": {"title": "Pricing Plans", "subtitle": "AI Virtual Try-On Solutions", "description": "Choose the perfect plan for your business needs and start transforming your customers' shopping experience"}, "faqs": {"title": "FAQ", "subtitle": "Frequently Asked Questions", "items": {"item-1": {"question": "How accurate is the AI virtual try-on?", "answer": "Our AI virtual try-on technology achieves 95%+ accuracy in fit prediction and realistic visualization, continuously improving through machine learning."}, "item-2": {"question": "What types of clothing can I try on virtually?", "answer": "Huiying supports all major clothing categories including tops, dresses, pants, jackets, and accessories. We're constantly expanding our supported item types."}, "item-3": {"question": "Do I need special equipment to use virtual try-on?", "answer": "No special equipment needed! Just use your smartphone camera or upload a photo. Our AI works with standard photos to create realistic try-on experiences."}, "item-4": {"question": "How does the size recommendation work?", "answer": "Our AI analyzes your body measurements from photos and compares them with garment specifications to recommend the best size, reducing returns by up to 40%."}, "item-5": {"question": "Can I integrate Huiying into my e-commerce store?", "answer": "Yes! We provide easy-to-integrate APIs and plugins for major e-commerce platforms. Contact our team for integration support."}}}, "testimonials": {"title": "Customer Success Stories", "subtitle": "What fashion retailers are saying about Huiying", "items": {"item-1": {"name": "<PERSON>", "role": "E-commerce Director", "image": "https://randomuser.me/api/portraits/women/1.jpg", "quote": "Huiying's AI virtual try-on increased our conversion rate by 35% and reduced returns by 40%. It's a game-changer for online fashion retail."}, "item-2": {"name": "<PERSON>", "role": "Fashion Brand Owner", "image": "https://randomuser.me/api/portraits/men/6.jpg", "quote": "The virtual try-on experience is so realistic that customers feel confident buying online. Our customer satisfaction scores have never been higher."}, "item-3": {"name": "<PERSON>", "role": "Digital Marketing Manager", "image": "https://randomuser.me/api/portraits/women/7.jpg", "quote": "Integration was seamless and the results were immediate. Customers love being able to see how clothes look before purchasing."}, "item-4": {"name": "<PERSON>", "role": "CTO, Fashion Startup", "image": "https://randomuser.me/api/portraits/men/8.jpg", "quote": "The API integration was straightforward and the AI accuracy is impressive. Our customers can now try on clothes virtually with confidence."}, "item-5": {"name": "<PERSON>", "role": "Retail Operations Manager", "image": "https://randomuser.me/api/portraits/women/4.jpg", "quote": "Huiying's virtual try-on technology has transformed our online shopping experience. Customer engagement has increased dramatically."}, "item-6": {"name": "<PERSON>", "role": "Fashion Tech Consultant", "image": "https://randomuser.me/api/portraits/men/2.jpg", "quote": "The most advanced virtual try-on solution I've seen. The AI accuracy and user experience are exceptional."}, "item-7": {"name": "<PERSON><PERSON><PERSON>", "role": "Founder of ChatExtend", "image": "https://randomuser.me/api/portraits/men/5.jpg", "quote": "enabling us to create UIs that are as stunning as they are user-friendly."}, "item-8": {"name": "<PERSON>", "role": "Fullstack Developer", "image": "https://randomuser.me/api/portraits/men/9.jpg", "quote": "Their extensive collection of UI components, blocks, and templates has significantly accelerated my workflow."}, "item-9": {"name": "<PERSON><PERSON><PERSON>", "role": "MerakiUI Creator", "image": "https://randomuser.me/api/portraits/men/10.jpg", "quote": "Responsive tailwind css components it's very helpful to start fast with your project."}, "item-10": {"name": "<PERSON>", "role": "TailwindAwesome Creator", "image": "https://randomuser.me/api/portraits/men/11.jpg", "quote": "The component blocks are well-structured, simple to use, and beautifully designed. It makes it really easy to have a good-looking website in no time."}, "item-11": {"name": "<PERSON>", "role": "@GoogleDevExpert for Android", "image": "https://randomuser.me/api/portraits/men/12.jpg", "quote": "Templates are the perfect solution for anyone who wants to create a beautiful and functional website without any web design experience."}, "item-12": {"name": "<PERSON>", "role": "Software Engineer", "image": "https://randomuser.me/api/portraits/men/13.jpg", "quote": "Designed that even with a very poor knowledge of web design you can do miracles. Let yourself be seduced!"}}}, "stats": {"title": "Impact Statistics", "subtitle": "<PERSON><PERSON>'s proven results", "description": "See how Huiying's AI virtual try-on technology is transforming fashion e-commerce worldwide", "items": {"item-1": {"title": "Fashion Brands Served"}, "item-2": {"title": "Virtual Try-Ons Daily"}, "item-3": {"title": "Return Rate Reduction"}}}, "calltoaction": {"title": "Transform Your Fashion Business", "description": "Join leading fashion brands using Huiying's AI virtual try-on technology to boost sales and reduce returns", "primaryButton": "Start Free Trial", "secondaryButton": "Book Demo"}}, "AITextPage": {"title": "AI Text Demo", "description": "Analyze web content with AI to extract key information, features, and insights", "content": "Web Content Analyzer", "subtitle": "Enter a website URL to get AI-powered analysis of its content", "analyzer": {"title": "Web Content Analyzer", "description": "Analyze any website content using AI to extract structured information", "placeholder": "Enter website URL (e.g., https://example.com)", "button": "Analyze Website", "loading": {"scraping": "Scraping website content...", "analyzing": "Analyzing content with AI..."}, "results": {"title": "Analysis Results", "newAnalysis": "Analyze Another Website", "sections": {"title": "Title", "description": "Description", "introduction": "Introduction", "features": "Features", "pricing": "Pricing", "useCases": "Use Cases", "screenshot": "Website Screenshot"}}, "errors": {"invalidUrl": "Please enter a valid URL starting with http:// or https://", "analysisError": "Failed to analyze website. Please try again.", "networkError": "Network error. Please check your connection and try again.", "insufficientCredits": "Insufficient credits. Please purchase more credits to continue."}}, "features": {"scraping": {"title": "Smart Web Scraping", "description": "Advanced web scraping technology extracts clean, structured content from any website"}, "analysis": {"title": "AI-Powered Analysis", "description": "Intelligent AI analysis extracts key insights, features, and structured information"}, "results": {"title": "Structured Results", "description": "Get organized, easy-to-read results with clear sections and actionable insights"}}}, "AIImagePage": {"title": "AI Virtual Try-On", "description": "Experience realistic virtual clothing try-on powered by advanced AI image processing technology", "content": "Virtual Try-On Studio"}, "AIVideoPage": {"title": "AI Fashion Video", "description": "Create dynamic fashion videos with AI-powered virtual try-on and styling recommendations", "content": "Fashion Video Creator"}, "AIAudioPage": {"title": "AI Style Assistant", "description": "Get personalized fashion advice and styling tips from our AI-powered voice assistant", "content": "Voice Style Assistant"}}