import Container from '@/components/layout/container';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import { ShirtIcon, SparklesIcon, UploadIcon } from 'lucide-react';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const pt = await getTranslations({ locale, namespace: 'OutfitPage' });

  return constructMetadata({
    title: pt('title') + ' | ' + t('title'),
    description: pt('description'),
    canonicalUrl: getUrlWithLocale('/outfit', locale),
  });
}

export default async function OutfitPage() {
  const t = await getTranslations('OutfitPage');

  return (
    <Container className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-primary/10 rounded-full">
              <ShirtIcon className="size-12 text-primary" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            {t('title')}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            {t('description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gap-2">
              <UploadIcon className="size-4" />
              {t('uploadPhoto')}
            </Button>
            <Button size="lg" variant="outline" className="gap-2">
              <SparklesIcon className="size-4" />
              {t('tryDemo')}
            </Button>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full w-fit mb-4">
                <UploadIcon className="size-6 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle>{t('features.upload.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>{t('features.upload.description')}</CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto p-3 bg-green-100 dark:bg-green-900/20 rounded-full w-fit mb-4">
                <SparklesIcon className="size-6 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle>{t('features.ai.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>{t('features.ai.description')}</CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full w-fit mb-4">
                <ShirtIcon className="size-6 text-purple-600 dark:text-purple-400" />
              </div>
              <CardTitle>{t('features.result.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>{t('features.result.description')}</CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Coming Soon Section */}
        <div className="text-center py-16 bg-muted/30 rounded-2xl">
          <h2 className="text-3xl font-bold mb-4">{t('comingSoon.title')}</h2>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            {t('comingSoon.description')}
          </p>
          <Button variant="outline" size="lg">
            {t('comingSoon.notify')}
          </Button>
        </div>
      </div>
    </Container>
  );
}
