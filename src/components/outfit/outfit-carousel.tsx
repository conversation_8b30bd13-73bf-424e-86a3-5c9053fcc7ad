'use client';

import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { SplitImage } from '@/types/outfit';
import { ChevronLeft, ChevronRight, ExternalLink, Shirt } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

interface OutfitCarouselProps {
  isOpen: boolean;
  onClose: () => void;
  splitImages: SplitImage[];
  outfitTitle?: string;
}

export function OutfitCarousel({
  isOpen,
  onClose,
  splitImages,
  outfitTitle = 'Outfit Details',
}: OutfitCarouselProps) {
  const t = useTranslations('OutfitPage.gallery');
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % splitImages.length);
  };

  const prevImage = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + splitImages.length) % splitImages.length
    );
  };

  const handleBuyNow = () => {
    const currentImage = splitImages[currentIndex];
    if (currentImage?.amazon_url) {
      window.open(currentImage.amazon_url, '_blank');
    }
  };

  const handleTryOn = () => {
    const currentImage = splitImages[currentIndex];
    if (currentImage?.url) {
      console.log('Try on image:', currentImage.url);
    }
  };

  if (splitImages.length === 0) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('modal.title')}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Carousel */}
          <div className="relative">
            <div className="aspect-square relative overflow-hidden rounded-lg bg-gray-100">
              <Image
                src={splitImages[currentIndex]?.url || ''}
                alt={`${splitImages[currentIndex]?.type || 'Item'} ${currentIndex + 1}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />

              {/* Navigation arrows */}
              {splitImages.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>

            {/* Dots indicator */}
            {splitImages.length > 1 && (
              <div className="flex justify-center space-x-2 mt-4">
                {splitImages.map((_, index) => (
                  <button
                    key={index}
                    type="button"
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                    }`}
                    onClick={() => setCurrentIndex(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Item info */}
          <div className="text-center">
            <p className="text-sm text-muted-foreground capitalize">
              {splitImages[currentIndex]?.type || 'Item'} ({currentIndex + 1} of{' '}
              {splitImages.length})
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex gap-3 justify-center">
            <Button
              onClick={handleBuyNow}
              className="flex-1 max-w-xs"
              disabled={!splitImages[currentIndex]?.amazon_url}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              {t('buttons.buyNow')}
            </Button>
            <Button
              variant="outline"
              onClick={handleTryOn}
              className="flex-1 max-w-xs"
            >
              <Shirt className="w-4 h-4 mr-2" />
              {t('buttons.tryOn')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
