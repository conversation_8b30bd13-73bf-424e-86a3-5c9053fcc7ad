'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { OutfitData } from '@/types/outfit';
import Image from 'next/image';
import { useState } from 'react';

interface OutfitCardProps {
  outfit: OutfitData;
  onClick: () => void;
  className?: string;
}

export function OutfitCard({ outfit, onClick, className }: OutfitCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  return (
    <Card
      className={cn(
        'group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]',
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-0">
        <div className="aspect-[3/4] relative overflow-hidden rounded-lg">
          {!imageError ? (
            <Image
              src={outfit.url}
              alt={`${outfit.sex} outfit ${outfit.id}`}
              fill
              className={cn(
                'object-cover transition-all duration-300 group-hover:scale-105',
                imageLoading ? 'blur-sm' : 'blur-0'
              )}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageError(true);
                setImageLoading(false);
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <div className="w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="text-sm">Image not available</p>
              </div>
            </div>
          )}

          {/* Loading overlay */}
          {imageLoading && !imageError && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse" />
          )}

          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />

          {/* Item count badge */}
          {outfit.split_images.length > 0 && (
            <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
              {outfit.split_images.length}{' '}
              {outfit.split_images.length === 1 ? 'item' : 'items'}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
