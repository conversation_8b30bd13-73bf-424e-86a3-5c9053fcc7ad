'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { OutfitData } from '@/types/outfit';
import { Sparkles } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

interface OutfitCardProps {
  outfit: OutfitData;
  onClick: () => void;
  className?: string;
}

export function OutfitCard({ outfit, onClick, className }: OutfitCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const t = useTranslations('OutfitPage.gallery.card');

  const handleTryOnClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(); // Open the outfit carousel
  };

  return (
    <Card
      className={cn(
        'group transition-all duration-200 hover:shadow-lg overflow-hidden',
        className
      )}
    >
      <CardContent className="p-0">
        {/* Image Section */}
        <div className="aspect-[3/4] relative overflow-hidden">
          {!imageError ? (
            <Image
              src={outfit.url}
              alt={`${outfit.sex} outfit ${outfit.id}`}
              fill
              className={cn(
                'object-cover transition-all duration-300 group-hover:scale-105',
                imageLoading ? 'blur-sm' : 'blur-0'
              )}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageError(true);
                setImageLoading(false);
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <div className="w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="text-sm">Image not available</p>
              </div>
            </div>
          )}

          {/* Loading overlay */}
          {imageLoading && !imageError && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse" />
          )}

          {/* Item count badge */}
          {outfit.split_images.length > 0 && (
            <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
              {outfit.split_images.length}{' '}
              {outfit.split_images.length === 1 ? t('item') : t('items')}
            </div>
          )}
        </div>

        {/* Info Section */}
        <div className="p-3 bg-white">
          {/* Gender Type */}
          <div className="text-sm text-muted-foreground mb-2">
            {t(`genderTypes.${outfit.sex.toLowerCase()}`)}
          </div>

          {/* Try On Button */}
          <button
            type="button"
            onClick={handleTryOnClick}
            className="w-full text-sm text-primary hover:text-primary/80 font-medium flex items-center justify-center gap-1 transition-colors"
          >
            <Sparkles className="w-3 h-3" />
            {useTranslations('OutfitPage.gallery.buttons')('tryOn')}
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
