'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { OutfitData } from '@/types/outfit';
import { Sparkles } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

interface OutfitCardProps {
  outfit: OutfitData;
  onClick: () => void;
  className?: string;
}

export function OutfitCard({ outfit, onClick, className }: OutfitCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const t = useTranslations('OutfitPage.gallery.card');

  const handleTryOnClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(); // Open the outfit carousel
  };

  const handleImageClick = () => {
    onClick(); // Open the outfit carousel when clicking on image
  };

  return (
    <Card
      className={cn(
        'group transition-all duration-200 hover:shadow-lg hover:bg-accent/50 overflow-hidden cursor-pointer rounded-xl border shadow-sm h-full',
        className
      )}
    >
      <CardContent className="p-0 h-full flex flex-col">
        {/* Image Section */}
        <div
          className="aspect-[3/4] relative overflow-hidden cursor-pointer bg-muted/30 rounded-t-xl flex-shrink-0"
          onClick={handleImageClick}
        >
          {!imageError ? (
            <Image
              src={outfit.url}
              alt={`${outfit.sex} outfit ${outfit.id}`}
              fill
              className={cn(
                'object-cover transition-all duration-300 group-hover:scale-105',
                imageLoading ? 'blur-sm' : 'blur-0'
              )}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageError(true);
                setImageLoading(false);
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <div className="w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="text-sm">Image not available</p>
              </div>
            </div>
          )}

          {/* Loading overlay */}
          {imageLoading && !imageError && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse" />
          )}

          {/* Item count badge */}
          {outfit.split_images.length > 0 && (
            <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2.5 py-1 rounded-full font-medium">
              {outfit.split_images.length}{' '}
              {outfit.split_images.length === 1 ? t('item') : t('items')}
            </div>
          )}
        </div>

        {/* Info Section */}
        <div className="px-4 py-3 bg-card flex-1 flex items-center">
          {/* Gender Type and Try On Button in one row */}
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-muted-foreground font-medium">
              {t(`genderTypes.${outfit.sex.toLowerCase()}`)}
            </div>
            <button
              type="button"
              onClick={handleTryOnClick}
              className="text-sm text-primary hover:text-primary/80 font-medium flex items-center gap-1.5 transition-colors px-2 py-1 rounded-md hover:bg-accent/50"
            >
              <Sparkles className="w-3.5 h-3.5" />
              {useTranslations('OutfitPage.gallery.buttons')('tryOn')}
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
