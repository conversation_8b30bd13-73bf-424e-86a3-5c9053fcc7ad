'use client';

import { OutfitCard } from '@/components/outfit/outfit-card';
import { OutfitCarousel } from '@/components/outfit/outfit-carousel';
import {
  type FilterType,
  OutfitFilter,
} from '@/components/outfit/outfit-filter';
import type { OutfitData } from '@/types/outfit';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

interface OutfitGalleryProps {
  outfits: OutfitData[];
}

export function OutfitGallery({ outfits }: OutfitGalleryProps) {
  const t = useTranslations('OutfitPage.gallery');
  const [currentFilter, setCurrentFilter] = useState<FilterType>('all');
  const [selectedOutfit, setSelectedOutfit] = useState<OutfitData | null>(null);
  const [isCarouselOpen, setIsCarouselOpen] = useState(false);

  // Filter outfits based on current filter
  const filteredOutfits = outfits.filter((outfit) => {
    if (currentFilter === 'all') return true;
    return outfit.sex.toLowerCase() === currentFilter;
  });

  const handleOutfitClick = (outfit: OutfitData) => {
    setSelectedOutfit(outfit);
    setIsCarouselOpen(true);
  };

  const handleCloseCarousel = () => {
    setIsCarouselOpen(false);
    setSelectedOutfit(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold">{t('title')}</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {t('description')}
        </p>
      </div>

      {/* Filter */}
      <div className="flex justify-end">
        <OutfitFilter
          currentFilter={currentFilter}
          onFilterChange={setCurrentFilter}
        />
      </div>

      {/* Gallery Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {filteredOutfits.map((outfit) => (
          <OutfitCard
            key={outfit.id}
            outfit={outfit}
            onClick={() => handleOutfitClick(outfit)}
          />
        ))}
      </div>

      {/* Empty state */}
      {filteredOutfits.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <p className="text-gray-500">
            No outfits found for the selected filter.
          </p>
        </div>
      )}

      {/* Carousel Modal */}
      {selectedOutfit && (
        <OutfitCarousel
          isOpen={isCarouselOpen}
          onClose={handleCloseCarousel}
          splitImages={selectedOutfit.split_images}
          outfitTitle={`${selectedOutfit.sex} outfit`}
        />
      )}
    </div>
  );
}
